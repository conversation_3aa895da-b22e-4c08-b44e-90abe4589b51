{"name": "Deny-Storage-Public", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy denies creation of storage accounts with public access enabled.", "displayName": "Deny Storage Public Access", "notScopes": [], "parameters": {}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751", "nonComplianceMessages": [{"message": "Storage accounts must not allow public access for security reasons."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}
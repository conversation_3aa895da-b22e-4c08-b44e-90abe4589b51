# Management Module - Custom Implementation
# This module provides management resources without dependency on Azure CAF Enterprise Scale module
# Includes: Log Analytics, Security Center, Azure Monitor Agent, and related services

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
  }
}

# Configure the Azure Provider
provider "azurerm" {
  features {}
}

# Get the current client configuration
data "azurerm_client_config" "current" {}

# Resource Group for Management Resources
resource "azurerm_resource_group" "management" {
  name     = "${var.root_id}-rg-management-${var.location}"
  location = var.location
  tags     = var.tags
}

# Log Analytics Module
module "log_analytics" {
  source = "./modules/log_analytics"

  resource_prefix     = var.root_id
  resource_group_name = azurerm_resource_group.management.name
  location            = var.location
  tags                = var.tags

  # Log Analytics Configuration
  retention_in_days = var.log_retention_in_days
  sku              = "PerGB2018"
  daily_quota_gb   = -1

  # Monitoring Configuration
  enable_monitoring_for_vm   = var.enable_monitoring_for_vm
  enable_monitoring_for_vmss = var.enable_monitoring_for_vmss
  enable_sentinel            = var.enable_sentinel
  enable_change_tracking     = var.enable_change_tracking
}

# Security Center Module
module "security_center" {
  source = "./modules/security_center"

  email_security_contact = var.security_alerts_email_address
  log_analytics_workspace_id = module.log_analytics.workspace_id

  # Microsoft Defender Plans
  enable_defender_for_apis                              = var.enable_defender_for_apis
  enable_defender_for_app_services                      = var.enable_defender_for_app_services
  enable_defender_for_arm                               = var.enable_defender_for_arm
  enable_defender_for_containers                        = var.enable_defender_for_containers
  enable_defender_for_cosmosdbs                         = var.enable_defender_for_cosmosdbs
  enable_defender_for_cspm                              = var.enable_defender_for_cspm
  enable_defender_for_dns                               = var.enable_defender_for_dns
  enable_defender_for_key_vault                         = var.enable_defender_for_key_vault
  enable_defender_for_oss_databases                     = var.enable_defender_for_oss_databases
  enable_defender_for_servers                           = var.enable_defender_for_servers
  enable_defender_for_servers_vulnerability_assessments = var.enable_defender_for_servers_vulnerability_assessments
  enable_defender_for_sql_servers                       = var.enable_defender_for_sql_servers
  enable_defender_for_sql_server_vms                    = var.enable_defender_for_sql_server_vms
  enable_defender_for_storage                           = var.enable_defender_for_storage

  depends_on = [module.log_analytics]
}

# Azure Monitor Agent Module
module "azure_monitor_agent" {
  source = "./modules/azure_monitor_agent"

  resource_prefix     = var.root_id
  resource_group_name = azurerm_resource_group.management.name
  location            = var.location
  tags                = var.tags

  # AMA Configuration
  enable_ama                           = var.enable_ama
  enable_vminsights_dcr                = var.enable_vminsights_dcr
  enable_change_tracking_dcr           = var.enable_change_tracking_dcr
  enable_mdfc_defender_for_sql_dcr     = var.enable_mdfc_defender_for_sql_dcr

  log_analytics_workspace_id = module.log_analytics.workspace_id

  depends_on = [module.log_analytics]
}



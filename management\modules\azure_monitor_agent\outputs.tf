# Azure Monitor Agent Module Outputs

output "user_assigned_identity_id" {
  description = "ID of the User Assigned Managed Identity for AMA"
  value       = var.enable_ama ? azurerm_user_assigned_identity.ama[0].id : null
}

output "user_assigned_identity_principal_id" {
  description = "Principal ID of the User Assigned Managed Identity for AMA"
  value       = var.enable_ama ? azurerm_user_assigned_identity.ama[0].principal_id : null
}

output "user_assigned_identity_client_id" {
  description = "Client ID of the User Assigned Managed Identity for AMA"
  value       = var.enable_ama ? azurerm_user_assigned_identity.ama[0].client_id : null
}

output "data_collection_rules" {
  description = "Data Collection Rules created by this module"
  value = {
    vm_insights = var.enable_vminsights_dcr ? {
      id   = azurerm_monitor_data_collection_rule.vm_insights[0].id
      name = azurerm_monitor_data_collection_rule.vm_insights[0].name
    } : null
    change_tracking = var.enable_change_tracking_dcr ? {
      id   = azurerm_monitor_data_collection_rule.change_tracking[0].id
      name = azurerm_monitor_data_collection_rule.change_tracking[0].name
    } : null
    defender_sql = var.enable_mdfc_defender_for_sql_dcr ? {
      id   = azurerm_monitor_data_collection_rule.defender_sql[0].id
      name = azurerm_monitor_data_collection_rule.defender_sql[0].name
    } : null
  }
}

output "vm_insights_dcr_id" {
  description = "ID of the VM Insights Data Collection Rule"
  value       = var.enable_vminsights_dcr ? azurerm_monitor_data_collection_rule.vm_insights[0].id : null
}

output "change_tracking_dcr_id" {
  description = "ID of the Change Tracking Data Collection Rule"
  value       = var.enable_change_tracking_dcr ? azurerm_monitor_data_collection_rule.change_tracking[0].id : null
}

output "defender_sql_dcr_id" {
  description = "ID of the Microsoft Defender for SQL Data Collection Rule"
  value       = var.enable_mdfc_defender_for_sql_dcr ? azurerm_monitor_data_collection_rule.defender_sql[0].id : null
}

{"non_productions": {"policy_assignments": ["Deny-Resource-Locations", "Deny-RSG-Locations", "Require-Mandatory-Tags", "Secure-Storage-Accounts", "Enforce-NSG", "Block-Public-IP-VMs", "Deny-KV-Not-Recoverable", "Deny-Storage-Public", "Deny-VM-Internet-SSH-RDP", "Enforce-Naming"], "policy_definitions": ["enforce_network_security_groups", "require_mandatory_tags_with_rules", "secure_storage_accounts", "naming_convention"], "policy_set_definitions": [], "role_definitions": [], "archetype_config": {"parameters": {"Deny-Resource-Locations": {"allowedLocations": ["eastasia", "southeastasia"]}, "Deny-RSG-Locations": {"allowedLocations": ["eastasia", "southeastasia"]}, "Require-Mandatory-Tags": {"Owner": "Owner", "org": "Organization", "created_by": "CreatedBy", "operation_team": "OperationTeam", "project_name": "ProjectName", "env": "Environment", "app_name": "ApplicationName", "resource_type": "ResourceType", "priority": "Priority", "data_zone": "DataZone"}}, "access_control": {}}}}
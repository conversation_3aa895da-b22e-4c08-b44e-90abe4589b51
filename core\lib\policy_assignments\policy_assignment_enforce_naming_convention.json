{"name": "Enforce-Naming", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy enforces naming conventions for Azure resources according to EWH Landing Zone organizational standards.", "displayName": "Enforce EWH Landing Zone Naming Convention", "notScopes": [], "parameters": {"orgPrefix": {"value": "ewh"}}, "policyDefinitionId": "${current_scope_resource_id}/providers/Microsoft.Authorization/policyDefinitions/naming_convention", "nonComplianceMessages": [{"message": "Resources must follow the EWH Landing Zone naming convention standards. All resources must start with 'ewh-' prefix followed by resource type abbreviation and descriptive name."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}
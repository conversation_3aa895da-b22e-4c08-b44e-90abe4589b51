# Security Center Module Variables

variable "email_security_contact" {
  type        = string
  description = "Email address for security contact"
  default     = null
}

variable "phone_security_contact" {
  type        = string
  description = "Phone number for security contact"
  default     = null
}

variable "log_analytics_workspace_id" {
  type        = string
  description = "Log Analytics workspace ID for integration"
  default     = null
}

# Microsoft Defender Plans
variable "enable_defender_for_apis" {
  type        = bool
  description = "Enable Microsoft Defender for APIs"
  default     = true
}

variable "enable_defender_for_app_services" {
  type        = bool
  description = "Enable Microsoft Defender for App Services"
  default     = true
}

variable "enable_defender_for_arm" {
  type        = bool
  description = "Enable Microsoft Defender for ARM"
  default     = true
}

variable "enable_defender_for_containers" {
  type        = bool
  description = "Enable Microsoft Defender for Containers"
  default     = true
}

variable "enable_defender_for_cosmosdbs" {
  type        = bool
  description = "Enable Microsoft Defender for Cosmos DB"
  default     = true
}

variable "enable_defender_for_cspm" {
  type        = bool
  description = "Enable Microsoft Defender for CSPM"
  default     = true
}

variable "enable_defender_for_dns" {
  type        = bool
  description = "Enable Microsoft Defender for DNS"
  default     = true
}

variable "enable_defender_for_key_vault" {
  type        = bool
  description = "Enable Microsoft Defender for Key Vault"
  default     = true
}

variable "enable_defender_for_oss_databases" {
  type        = bool
  description = "Enable Microsoft Defender for Open Source Databases"
  default     = true
}

variable "enable_defender_for_servers" {
  type        = bool
  description = "Enable Microsoft Defender for Servers"
  default     = true
}

variable "enable_defender_for_servers_vulnerability_assessments" {
  type        = bool
  description = "Enable vulnerability assessments for Microsoft Defender for Servers"
  default     = true
}

variable "enable_defender_for_sql_servers" {
  type        = bool
  description = "Enable Microsoft Defender for SQL Servers"
  default     = true
}

variable "enable_defender_for_sql_server_vms" {
  type        = bool
  description = "Enable Microsoft Defender for SQL Server VMs"
  default     = true
}

variable "enable_defender_for_storage" {
  type        = bool
  description = "Enable Microsoft Defender for Storage"
  default     = true
}

variable "enable_defender_for_storage_malware_scanning" {
  type        = bool
  description = "Enable malware scanning for Microsoft Defender for Storage"
  default     = true
}

variable "defender_for_storage_malware_scanning_cap_gb_per_month" {
  type        = number
  description = "Monthly cap in GB for malware scanning"
  default     = 5000
}

variable "enable_defender_for_storage_sensitive_data_discovery" {
  type        = bool
  description = "Enable sensitive data discovery for Microsoft Defender for Storage"
  default     = true
}

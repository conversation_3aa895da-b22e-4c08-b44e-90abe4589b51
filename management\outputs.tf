# Management Module Outputs
# Custom implementation outputs

# Resource Group
output "resource_group_id" {
  description = "ID of the management resource group"
  value       = azurerm_resource_group.management.id
}

output "resource_group_name" {
  description = "Name of the management resource group"
  value       = azurerm_resource_group.management.name
}

# Log Analytics Outputs
output "log_analytics_workspace_id" {
  description = "ID of the Log Analytics workspace"
  value       = module.log_analytics.workspace_id
}

output "log_analytics_workspace_name" {
  description = "Name of the Log Analytics workspace"
  value       = module.log_analytics.workspace_name
}

output "log_analytics_workspace_customer_id" {
  description = "Customer ID (workspace ID) for the Log Analytics workspace"
  value       = module.log_analytics.workspace_customer_id
}

output "log_analytics_primary_shared_key" {
  description = "Primary shared key for the Log Analytics workspace"
  value       = module.log_analytics.primary_shared_key
  sensitive   = true
}

output "log_analytics_solution_ids" {
  description = "IDs of deployed Log Analytics solutions"
  value       = module.log_analytics.solution_ids
}

output "sentinel_id" {
  description = "ID of Microsoft Sentinel onboarding"
  value       = module.log_analytics.sentinel_id
}

# Security Center Outputs
output "security_center_contact_id" {
  description = "ID of the Security Center contact"
  value       = module.security_center.security_center_contact_id
}

output "defender_plan_ids" {
  description = "IDs of Microsoft Defender plans"
  value       = module.security_center.defender_plan_ids
}

output "auto_provisioning_id" {
  description = "ID of the auto provisioning setting"
  value       = module.security_center.auto_provisioning_id
}

# Azure Monitor Agent Outputs
output "ama_user_assigned_identity_id" {
  description = "ID of the User Assigned Managed Identity for AMA"
  value       = module.azure_monitor_agent.user_assigned_identity_id
}

output "data_collection_rules" {
  description = "Data Collection Rules created by the module"
  value       = module.azure_monitor_agent.data_collection_rules
}

output "vm_insights_dcr_id" {
  description = "ID of the VM Insights Data Collection Rule"
  value       = module.azure_monitor_agent.vm_insights_dcr_id
}

output "change_tracking_dcr_id" {
  description = "ID of the Change Tracking Data Collection Rule"
  value       = module.azure_monitor_agent.change_tracking_dcr_id
}

output "defender_sql_dcr_id" {
  description = "ID of the Microsoft Defender for SQL Data Collection Rule"
  value       = module.azure_monitor_agent.defender_sql_dcr_id
}

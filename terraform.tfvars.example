# ==============================================================================
# EWH Landing Zone - Terraform Configuration Example
# ==============================================================================
# Copy this file to terraform.tfvars and update with your actual values

# ==============================================================================
# BASIC CONFIGURATION
# ==============================================================================

# Root Management Group Configuration
root_id   = "ewh"           # Unique identifier for your organization
root_name = "EWH"           # Display name for the root management group

# Azure Regions
primary_location   = "southeastasia"  # Primary region for resources
secondary_location = "eastasia"       # Secondary region for disaster recovery

# Security Contact
email_security_contact = "<EMAIL>"  # Replace with actual security email

# Log Retention
log_retention_in_days = 60  # How long to keep logs (30-365 days)

# ==============================================================================
# SUBSCRIPTION CONFIGURATION
# ==============================================================================
# Choose one of the scenarios below based on your requirements

# ==============================================================================
# SCENARIO 1: SINGLE SUBSCRIPTION (Development/Testing)
# ==============================================================================
# All resources deployed in the current subscription
# subscription_id_management   = ""  # Leave empty to use current subscription
# subscription_id_connectivity = ""  # Will fallback to management subscription
# subscription_id_identity     = ""  # Will fallback to management subscription

# ==============================================================================
# SCENARIO 2: MULTI-SUBSCRIPTION (Production - Recommended)
# ==============================================================================
# Each layer has its own dedicated subscription
subscription_id_management   = "11111111-1111-1111-1111-111111111111"  # Management subscription
subscription_id_connectivity = "22222222-2222-2222-2222-222222222222"  # Networking subscription
subscription_id_identity     = "33333333-3333-3333-3333-333333333333"  # Identity subscription

# ==============================================================================
# SCENARIO 3: HYBRID APPROACH
# ==============================================================================
# Core + Management share subscription, Connectivity has separate subscription
# subscription_id_management   = "11111111-1111-1111-1111-111111111111"  # Management subscription
# subscription_id_connectivity = "22222222-2222-2222-2222-222222222222"  # Networking subscription
# subscription_id_identity     = ""  # Will fallback to management subscription

# ==============================================================================
# NETWORKING CONFIGURATION
# ==============================================================================

# DDoS Protection
enable_ddos_protection = true  # Enable DDoS protection for hub virtual networks

# ==============================================================================
# AZURE AD GROUPS CONFIGURATION
# ==============================================================================

# Example Azure AD Groups (uncomment and modify as needed)
# azure_ad_groups = {
#   "ewh-landing-zone-admins" = {
#     display_name       = "EWH Landing Zone Administrators"
#     description        = "Administrators for the EWH Landing Zone"
#     security_enabled   = true
#     assignable_to_role = true
#     mail_enabled       = false
#     members            = ["<EMAIL>", "<EMAIL>"]
#     additional_owners  = ["<EMAIL>"]
#     azure_roles        = ["Owner", "Contributor"]
#     directory_roles    = []
#   }
#   "ewh-security-team" = {
#     display_name       = "EWH Security Team"
#     description        = "Security team members"
#     security_enabled   = true
#     assignable_to_role = true
#     mail_enabled       = false
#     members            = ["<EMAIL>", "<EMAIL>"]
#     additional_owners  = ["<EMAIL>"]
#     azure_roles        = ["Security Reader", "Security Admin"]
#     directory_roles    = []
#   }
# }

# Role Assignment Configuration
assign_roles_to_all_subscriptions = false  # Set to true to assign roles across all subscriptions
enable_directory_role_assignments = false   # Set to true if you have Privileged Role Administrator permissions

# ==============================================================================
# TAGGING CONFIGURATION
# ==============================================================================

# Default tags for all resources - MUST include all mandatory tags
tags = {
  # Mandatory Tags (Required by Policy)
  Owner           = "<EMAIL>"
  Organization    = "EWH"
  CreatedBy       = "<EMAIL>"
  OperationTeam   = "Platform Team"
  ProjectName     = "EWH Landing Zone"
  Environment     = "Production"
  ApplicationName = "Platform Infrastructure"
  ResourceType    = "Infrastructure"
  Priority        = "High"
  DataZone        = "Internal"
  CostCenter      = "IT-001"
  ManagedBy       = "Terraform"
  
  # Additional Tags (Optional but Recommended)
  BusinessUnit    = "IT"
  Compliance      = "ISO27001"
  BackupRequired  = "Yes"
  Monitoring      = "Yes"
  DRPlan          = "Yes"
}

# Additional tags for connectivity resources
connectivity_resources_tags = {
  Purpose   = "Networking"
  Component = "Connectivity"
  Network   = "Hub-Spoke"
  Traffic   = "Internal"
}

# Additional tags for management resources
management_resources_tags = {
  Purpose   = "Management"
  Component = "Monitoring"
  Service   = "Platform Operations"
}

# ==============================================================================
# DEPLOYMENT NOTES
# ==============================================================================
# 
# 1. SUBSCRIPTION SETUP:
#    - Create subscriptions in Azure Portal
#    - Assign Owner role to your service principal
#    - Note down the Subscription IDs
#
# 2. PERMISSIONS REQUIRED:
#    - Core Module: Global Administrator or Privileged Role Administrator
#    - Management Module: Owner or Contributor on management subscription
#    - Connectivity Module: Owner or Contributor on connectivity subscription
#
# 3. DEPLOYMENT ORDER:
#    - Core → Management → Connectivity
#    - Use terraform apply -target=module.core first
#    - Then terraform apply -target=module.management
#    - Finally terraform apply -target=module.connectivity
#
# 4. VALIDATION:
#    - Check Management Groups are created
#    - Verify Policies are assigned
#    - Confirm resources are in correct subscriptions
#    - Validate all resources have proper tags
#    - Verify naming conventions are followed
#
# 5. MANDATORY TAGS:
#    - All resources MUST have the 12 mandatory tags defined above
#    - Tags are enforced by Azure Policy
#    - Resources without proper tags will be denied creation
#
# 6. NAMING CONVENTION:
#    - All resources MUST start with 'ewh-' prefix
#    - Follow the pattern: ewh-{resource-type}-{descriptive-name}
#    - Examples: ewh-rg-management-southeastasia, ewh-vnet-hub-southeastasia
#
# ==============================================================================

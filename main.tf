# Configure Terraform to set the required AzureRM provider
# version and features{} block

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.107"
    }
    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 2.47"
    }
  }
}

# Define the provider configuration
provider "azurerm" {
  features {}
}

# Get the current client configuration from the AzureRM provider
data "azurerm_client_config" "current" {}

# Logic to handle 1-3 platform subscriptions as available
locals {
  subscription_id_connectivity = coalesce(var.subscription_id_connectivity, local.subscription_id_management)
  subscription_id_identity     = coalesce(var.subscription_id_identity, local.subscription_id_management)
  subscription_id_management   = coalesce(var.subscription_id_management, data.azurerm_client_config.current.subscription_id)
}

# ==============================================================================
# MODULE ORCHESTRATION
# ==============================================================================
# The following module declarations act to orchestrate the
# independently defined module instances for core, connectivity and management resources

# Core module for Management Groups and Policies (Custom Implementation)
# This module should be deployed first as it creates the foundational structure
module "core" {
  source = "./core"

  root_id        = var.root_id
  root_name      = var.root_name

  # Azure AD Groups configuration
  azure_ad_groups = var.azure_ad_groups
  assign_roles_to_all_subscriptions = var.assign_roles_to_all_subscriptions
  enable_directory_role_assignments = var.enable_directory_role_assignments
}

# Management module for Log Analytics, Security Center, and AMA (Custom Implementation)
# This module depends on core module and provides management infrastructure
module "management" {
  source = "./management"

  root_id                        = var.root_id
  root_name                      = var.root_name
  location                       = var.primary_location
  security_alerts_email_address = var.email_security_contact
  log_retention_in_days          = var.log_retention_in_days

  # Advanced monitoring configuration
  enable_ama                           = true
  enable_vminsights_dcr                = true
  enable_change_tracking_dcr           = true
  enable_mdfc_defender_for_sql_dcr     = false
  enable_monitoring_for_vm             = true
  enable_monitoring_for_vmss           = true
  enable_sentinel                      = true
  enable_change_tracking               = true

  # Microsoft Defender for Cloud configuration
  enable_defender_for_apis                              = true
  enable_defender_for_app_services                      = true
  enable_defender_for_arm                               = true
  enable_defender_for_containers                        = true
  enable_defender_for_cosmosdbs                         = true
  enable_defender_for_cspm                              = true
  enable_defender_for_dns                               = true
  enable_defender_for_key_vault                         = true
  enable_defender_for_oss_databases                     = true
  enable_defender_for_servers                           = true
  enable_defender_for_servers_vulnerability_assessments = true
  enable_defender_for_sql_servers                       = true
  enable_defender_for_sql_server_vms                    = true
  enable_defender_for_storage                           = true

  # Tags
  tags = var.tags
}

# Connectivity module for Hub and Spoke networking (Custom Implementation)
# This module provides networking infrastructure and depends on core module
module "connectivity" {
  source = "./connectivity"

  root_id                        = var.root_id
  root_name                      = var.root_name
  primary_location               = var.primary_location
  secondary_location             = var.secondary_location
  subscription_id_connectivity   = local.subscription_id_connectivity

  # Networking configuration
  enable_ddos_protection         = var.enable_ddos_protection
  connectivity_resources_tags    = var.connectivity_resources_tags

  # Tags
  tags = var.tags
}

# ==============================================================================
# OUTPUTS
# ==============================================================================
# Output values from all modules for reference and integration

output "core_outputs" {
  description = "Outputs from the core module"
  value = module.core
}

output "management_outputs" {
  description = "Outputs from the management module"
  value = module.management
  sensitive = true
}

output "connectivity_outputs" {
  description = "Outputs from the connectivity module"
  value = module.connectivity
}

output "deployment_summary" {
  description = "Summary of the landing zone deployment"
  value = {
    root_id                      = var.root_id
    root_name                    = var.root_name
    primary_location             = var.primary_location
    secondary_location           = var.secondary_location
    subscription_id_management   = local.subscription_id_management
    subscription_id_connectivity = local.subscription_id_connectivity
    subscription_id_identity     = local.subscription_id_identity
    deployment_timestamp         = timestamp()
  }
}


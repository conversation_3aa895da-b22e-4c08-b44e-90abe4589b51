# Log Analytics Module Outputs

output "workspace_id" {
  description = "ID of the Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.main.id
}

output "workspace_name" {
  description = "Name of the Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.main.name
}

output "workspace_customer_id" {
  description = "Customer ID (workspace ID) for the Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.main.workspace_id
}

output "primary_shared_key" {
  description = "Primary shared key for the Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.main.primary_shared_key
  sensitive   = true
}

output "secondary_shared_key" {
  description = "Secondary shared key for the Log Analytics workspace"
  value       = azurerm_log_analytics_workspace.main.secondary_shared_key
  sensitive   = true
}

output "solution_ids" {
  description = "IDs of deployed Log Analytics solutions"
  value = {
    vm_insights             = var.enable_monitoring_for_vm ? azurerm_log_analytics_solution.vm_insights[0].id : null
    change_tracking         = var.enable_change_tracking ? azurerm_log_analytics_solution.change_tracking[0].id : null
    updates                 = var.enable_updates ? azurerm_log_analytics_solution.updates[0].id : null
    security                = var.enable_security ? azurerm_log_analytics_solution.security[0].id : null
    agent_health_assessment = var.enable_agent_health_assessment ? azurerm_log_analytics_solution.agent_health_assessment[0].id : null
    anti_malware            = var.enable_anti_malware ? azurerm_log_analytics_solution.anti_malware[0].id : null
    container_insights      = var.enable_container_insights ? azurerm_log_analytics_solution.container_insights[0].id : null
  }
}

output "sentinel_id" {
  description = "ID of Microsoft Sentinel onboarding"
  value       = var.enable_sentinel ? azurerm_sentinel_log_analytics_workspace_onboarding.main[0].id : null
}

output "workspace_resource" {
  description = "Complete Log Analytics workspace resource"
  value       = azurerm_log_analytics_workspace.main
}

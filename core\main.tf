# Get the current client configuration from the AzureRM provider.
# This is used to populate the root_parent_id variable with the
# current Tenant ID used as the ID for the "Tenant Root Group"
# Management Group.

data "azurerm_client_config" "core" {}

# Declare the Azure landing zones Terraform module
# and provide a base configuration.

module "enterprise_scale" {
  source  = "Azure/caf-enterprise-scale/azurerm"
  version = "6.3.1" # change this to your desired version, https://www.terraform.io/language/expressions/version-constraints

  default_location = "eastasia"

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm
    azurerm.management   = azurerm
  }

  root_parent_id = data.azurerm_client_config.core.tenant_id
  root_id        = var.root_id
  root_name      = var.root_name
  library_path   = "${path.module}/lib"

  custom_landing_zones = {
    "${var.root_id}-productions" = {
      display_name               = "Productions"
      parent_management_group_id = "${var.root_id}-landing-zones"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "productions"
        parameters     = {}
        access_control = {}
      }
    }
    "${var.root_id}-non-productions" = {
      display_name               = "Non Productions"
      parent_management_group_id = "${var.root_id}-landing-zones"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "non_productions"
        parameters     = {}
        access_control = {}
      }
    }
    "${var.root_id}-product-a" = {
      display_name               = "Product A"
      parent_management_group_id = "${var.root_id}-productions"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "product_a"
        parameters     = {}
        access_control = {}
      }
    }
    "${var.root_id}-product-b" = {
      display_name               = "Product B"
      parent_management_group_id = "${var.root_id}-productions"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "product_b"
        parameters     = {}
        access_control = {}
      }
    }
    "${var.root_id}-uat" = {
      display_name               = "UAT"
      parent_management_group_id = "${var.root_id}-non-productions"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "uat"
        parameters     = {}
        access_control = {}
      }
    }
    "${var.root_id}-devtest" = {
      display_name               = "DevTest"
      parent_management_group_id = "${var.root_id}-non-productions"
      subscription_ids           = []
      archetype_config = {
        archetype_id   = "devtest"
        parameters     = {}
        access_control = {}
      }
    }
  }
}

# Azure AD Groups Module
module "azure_ad_groups" {
  source = "./modules/azure_ad_groups"

  groups = var.azure_ad_groups

  assign_roles_to_all_subscriptions = var.assign_roles_to_all_subscriptions
  enable_directory_role_assignments = var.enable_directory_role_assignments

  tags = {
    Environment = "Production"
    ManagedBy   = "Terraform"
    Project     = "Landing Zone"
  }
}
{"name": "Deny-KV-Not-Recoverable", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy denies creation of KeyVault objects that are not recoverable (soft delete and purge protection disabled).", "displayName": "<PERSON><PERSON> Objects Not Recoverable", "notScopes": [], "parameters": {}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0b60c0b2-2dc2-4e1c-b5c9-abbed971de53", "nonComplianceMessages": [{"message": "KeyVault objects must have soft delete and purge protection enabled for recoverability."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}
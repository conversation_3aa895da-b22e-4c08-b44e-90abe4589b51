{"name": "Deny-Resource-Locations", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "Specifies the allowed locations (regions) where Resources can be deployed using custom location policy.", "displayName": "Limit allowed locations for Resources (Custom)", "notScopes": [], "parameters": {"allowedLocations": {"value": ["eastasia", "southeastasia"]}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e56962a6-4747-49cd-b67b-bf8b01975c4c", "nonComplianceMessages": [{"message": "Resources {enforcementMode} only be deployed to the allowed locations."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}
{"name": "Deny-VM-Internet-SSH-RDP", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2019-09-01", "properties": {"description": "This policy denies creation of VMs with direct internet access via SSH/RDP ports.", "displayName": "Deny VM Internet Access SSH/RDP", "notScopes": [], "parameters": {}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "nonComplianceMessages": [{"message": "Virtual machines must not have direct internet access via SSH/RDP ports for security reasons."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}
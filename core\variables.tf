# Use variables to customize the deployment

variable "root_id" {
  type    = string
  default = "myorg"
}

variable "root_name" {
  type    = string
  default = "My Organization"
}

variable "azure_ad_groups" {
  description = "Map of Azure AD groups to create with their configurations"
  type = map(object({
    display_name         = string
    description          = string
    security_enabled     = bool
    assignable_to_role   = bool
    mail_enabled         = optional(bool, false)
    members              = optional(list(string), [])
    additional_owners    = optional(list(string), [])
    azure_roles          = optional(list(string), [])
    directory_roles      = optional(list(string), [])
  }))
  default = {}
}

variable "assign_roles_to_all_subscriptions" {
  type        = bool
  description = "If set to true, will assign Azure roles to all subscriptions in the tenant. If false, only assigns to current subscription."
  default     = false
}

variable "enable_directory_role_assignments" {
  type        = bool
  description = "If set to true, will enable Azure AD directory role assignments. Requires Privileged Role Administrator permissions."
  default     = false
}
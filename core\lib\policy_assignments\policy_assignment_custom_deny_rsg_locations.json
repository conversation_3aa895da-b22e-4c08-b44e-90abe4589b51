{"name": "Deny-RSG-Locations", "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "properties": {"description": "Specifies the allowed locations (regions) where Resource Groups can be deployed using custom location policy.", "displayName": "Limit allowed locations for Resource Groups (Custom)", "notScopes": [], "parameters": {"allowedLocations": {"value": ["eastasia", "southeastasia"]}}, "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e765b5de-1225-4ba3-bd56-1ac6695af988", "nonComplianceMessages": [{"message": "Resource groups {enforcementMode} only be deployed to the allowed locations."}], "scope": "${current_scope_resource_id}", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "location": "${default_location}", "identity": {"type": "None"}}